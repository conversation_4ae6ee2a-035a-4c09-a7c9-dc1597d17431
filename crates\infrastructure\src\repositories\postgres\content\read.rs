use async_trait::async_trait;
use reforged_domain::repository::content_repository::ContentReadRepository;
use reforged_domain::{
    error::RepositoryError,
    models::{
        achievement::{entity::Achievement, value_object::AchievementId},
        book::{entity::Book, value_object::BookId},
        class::{entity::Class, value_object::ClassId},
        class_category::{entity::ClassCategory, value_object::ClassCategoryId},
        class_skill::{entity::ClassSkill, value_object::ClassSkillId},
        faction::{entity::Faction, value_object::FactionId},
        quest::{entity::Quest, value_object::QuestId},
        skill::{entity::Skill, value_object::SkillId},
        title::{entity::Title, value_object::TitleId},
    },
};
use sea_orm::DatabaseConnection;

pub struct PostgresContentReadRepository {
    pool: DatabaseConnection,
}

impl PostgresContentReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ContentReadRepository for PostgresContentReadRepository {
    // Achievement queries
    async fn find_achievement_by_id(
        &self,
        id: &AchievementId,
    ) -> Result<Option<Achievement>, RepositoryError> {
        todo!()
    }

    async fn find_achievements_by_name(
        &self,
        name: &str,
    ) -> Result<Vec<Achievement>, RepositoryError> {
        todo!()
    }

    async fn find_all_achievements(&self) -> Result<Vec<Achievement>, RepositoryError> {
        todo!()
    }

    // Book queries
    async fn find_book_by_id(&self, id: &BookId) -> Result<Option<Book>, RepositoryError> {
        todo!()
    }

    async fn find_books_by_name(&self, name: &str) -> Result<Vec<Book>, RepositoryError> {
        todo!()
    }

    async fn find_all_books(&self) -> Result<Vec<Book>, RepositoryError> {
        todo!()
    }

    // Class queries
    async fn find_class_by_id(&self, id: &ClassId) -> Result<Option<Class>, RepositoryError> {
        todo!()
    }

    async fn find_classes_by_name(&self, name: &str) -> Result<Vec<Class>, RepositoryError> {
        todo!()
    }

    async fn find_classes_by_category(
        &self,
        category_id: &ClassCategoryId,
    ) -> Result<Vec<Class>, RepositoryError> {
        todo!()
    }

    async fn find_all_classes(&self) -> Result<Vec<Class>, RepositoryError> {
        todo!()
    }

    // Class Category queries
    async fn find_class_category_by_id(
        &self,
        id: &ClassCategoryId,
    ) -> Result<Option<ClassCategory>, RepositoryError> {
        todo!()
    }

    async fn find_class_category_by_name(
        &self,
        name: &str,
    ) -> Result<Option<ClassCategory>, RepositoryError> {
        todo!()
    }

    async fn find_all_class_categories(&self) -> Result<Vec<ClassCategory>, RepositoryError> {
        todo!()
    }

    // Class Skill queries
    async fn find_class_skill_by_id(
        &self,
        id: &ClassSkillId,
    ) -> Result<Option<ClassSkill>, RepositoryError> {
        todo!()
    }

    async fn find_class_skills_by_class(
        &self,
        class_id: &ClassId,
    ) -> Result<Vec<ClassSkill>, RepositoryError> {
        todo!()
    }

    async fn find_class_skills_by_skill(
        &self,
        skill_id: &SkillId,
    ) -> Result<Vec<ClassSkill>, RepositoryError> {
        todo!()
    }

    // Faction queries
    async fn find_faction_by_id(&self, id: &FactionId) -> Result<Option<Faction>, RepositoryError> {
        todo!()
    }

    async fn find_faction_by_name(&self, name: &str) -> Result<Option<Faction>, RepositoryError> {
        todo!()
    }

    async fn find_all_factions(&self) -> Result<Vec<Faction>, RepositoryError> {
        todo!()
    }

    // Quest queries
    async fn find_quest_by_id(&self, id: &QuestId) -> Result<Option<Quest>, RepositoryError> {
        todo!()
    }

    async fn find_quests_by_name(&self, name: &str) -> Result<Vec<Quest>, RepositoryError> {
        todo!()
    }

    async fn find_all_quests(&self) -> Result<Vec<Quest>, RepositoryError> {
        todo!()
    }

    // Skill queries
    async fn find_skill_by_id(&self, id: &SkillId) -> Result<Option<Skill>, RepositoryError> {
        todo!()
    }

    async fn find_skill_by_name(&self, name: &str) -> Result<Option<Skill>, RepositoryError> {
        todo!()
    }

    async fn find_all_skills(&self) -> Result<Vec<Skill>, RepositoryError> {
        todo!()
    }

    // Title queries
    async fn find_title_by_id(&self, id: &TitleId) -> Result<Option<Title>, RepositoryError> {
        todo!()
    }

    async fn find_title_by_name(&self, name: &str) -> Result<Option<Title>, RepositoryError> {
        todo!()
    }

    async fn find_all_titles(&self) -> Result<Vec<Title>, RepositoryError> {
        todo!()
    }
}
