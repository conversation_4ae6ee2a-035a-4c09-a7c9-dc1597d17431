use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        user_livedrop::{entity::UserLivedrop, value_object::UserLivedropId},
        war::{entity::War, value_object::WarId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait EventRepository: Send + Sync {
    // User Livedrop operations
    async fn create_user_livedrop(&self, livedrop: &UserLivedrop) -> Result<UserLivedrop, RepositoryError>;
    async fn update_user_livedrop(&self, livedrop: &UserLivedrop) -> Result<(), RepositoryError>;
    async fn delete_user_livedrop(&self, id: &UserLivedropId) -> Result<(), RepositoryError>;
    
    // War operations
    async fn create_war(&self, war: &War) -> Result<War, RepositoryError>;
    async fn update_war(&self, war: &War) -> Result<(), RepositoryError>;
    async fn delete_war(&self, id: &WarId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait EventReadRepository: Send + Sync {
    // User Livedrop queries
    async fn find_user_livedrop_by_id(&self, id: &UserLivedropId) -> Result<Option<UserLivedrop>, RepositoryError>;
    async fn find_user_livedrops_by_user(&self, user_id: &crate::models::user::value_object::UserId) -> Result<Vec<UserLivedrop>, RepositoryError>;
    async fn find_all_user_livedrops(&self) -> Result<Vec<UserLivedrop>, RepositoryError>;
    async fn find_active_user_livedrops(&self) -> Result<Vec<UserLivedrop>, RepositoryError>;
    
    // War queries
    async fn find_war_by_id(&self, id: &WarId) -> Result<Option<War>, RepositoryError>;
    async fn find_wars_by_name(&self, name: &str) -> Result<Vec<War>, RepositoryError>;
    async fn find_all_wars(&self) -> Result<Vec<War>, RepositoryError>;
    async fn find_active_wars(&self) -> Result<Vec<War>, RepositoryError>;
}
