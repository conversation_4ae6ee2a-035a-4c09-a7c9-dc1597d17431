use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        guild::{entity::Guild, value_object::GuildId},
        guild_hall::{entity::GuildHall, value_object::GuildHallId},
        guild_hall_building::{entity::GuildHallBuilding, value_object::GuildHallBuildingId},
        guild_hall_connection::{entity::GuildHallConnection, value_object::GuildHallConnectionId},
        guild_inventory::{entity::GuildInventory, value_object::GuildInventoryId},
        guild_level::{entity::GuildLevel, value_object::GuildLevelId},
        user_guild::{entity::UserGuild, value_object::UserGuildId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait GuildRepository: Send + Sync {
    // Guild CRUD operations
    async fn create(&self, guild: &Guild) -> Result<Guild, RepositoryError>;
    async fn update(&self, guild: &Guild) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &GuildId) -> Result<(), RepositoryError>;

    // Guild Hall operations
    async fn create_hall(&self, hall: &GuildHall) -> Result<GuildHall, RepositoryError>;
    async fn update_hall(&self, hall: &GuildHall) -> Result<(), RepositoryError>;
    async fn delete_hall(&self, id: &GuildHallId) -> Result<(), RepositoryError>;

    // Guild Hall Building operations
    async fn create_hall_building(&self, building: &GuildHallBuilding) -> Result<GuildHallBuilding, RepositoryError>;
    async fn update_hall_building(&self, building: &GuildHallBuilding) -> Result<(), RepositoryError>;
    async fn delete_hall_building(&self, id: &GuildHallBuildingId) -> Result<(), RepositoryError>;

    // Guild Hall Connection operations
    async fn create_hall_connection(&self, connection: &GuildHallConnection) -> Result<GuildHallConnection, RepositoryError>;
    async fn delete_hall_connection(&self, id: &GuildHallConnectionId) -> Result<(), RepositoryError>;

    // Guild Inventory operations
    async fn create_inventory_item(&self, item: &GuildInventory) -> Result<GuildInventory, RepositoryError>;
    async fn update_inventory_item(&self, item: &GuildInventory) -> Result<(), RepositoryError>;
    async fn delete_inventory_item(&self, id: &GuildInventoryId) -> Result<(), RepositoryError>;

    // Guild Level operations
    async fn create_level(&self, level: &GuildLevel) -> Result<GuildLevel, RepositoryError>;
    async fn update_level(&self, level: &GuildLevel) -> Result<(), RepositoryError>;
    async fn delete_level(&self, id: &GuildLevelId) -> Result<(), RepositoryError>;

    // User Guild membership operations
    async fn add_member(&self, membership: &UserGuild) -> Result<UserGuild, RepositoryError>;
    async fn update_member(&self, membership: &UserGuild) -> Result<(), RepositoryError>;
    async fn remove_member(&self, id: &UserGuildId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait GuildReadRepository: Send + Sync {
    // Guild queries
    async fn find_by_id(&self, id: &GuildId) -> Result<Option<Guild>, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Guild>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Guild>, RepositoryError>;
    async fn find_paginate(&self, page: u32, limit: u32) -> Result<Vec<Guild>, RepositoryError>;

    // Guild Hall queries
    async fn find_hall_by_id(&self, id: &GuildHallId) -> Result<Option<GuildHall>, RepositoryError>;
    async fn find_halls_by_guild_id(&self, guild_id: &GuildId) -> Result<Vec<GuildHall>, RepositoryError>;

    // Guild Hall Building queries
    async fn find_hall_building_by_id(&self, id: &GuildHallBuildingId) -> Result<Option<GuildHallBuilding>, RepositoryError>;
    async fn find_hall_buildings_by_hall_id(&self, hall_id: &GuildHallId) -> Result<Vec<GuildHallBuilding>, RepositoryError>;

    // Guild Hall Connection queries
    async fn find_hall_connection_by_id(&self, id: &GuildHallConnectionId) -> Result<Option<GuildHallConnection>, RepositoryError>;
    async fn find_hall_connections_by_hall_id(&self, hall_id: &GuildHallId) -> Result<Vec<GuildHallConnection>, RepositoryError>;

    // Guild Inventory queries
    async fn find_inventory_item_by_id(&self, id: &GuildInventoryId) -> Result<Option<GuildInventory>, RepositoryError>;
    async fn find_inventory_by_guild_id(&self, guild_id: &GuildId) -> Result<Vec<GuildInventory>, RepositoryError>;

    // Guild Level queries
    async fn find_level_by_id(&self, id: &GuildLevelId) -> Result<Option<GuildLevel>, RepositoryError>;
    async fn find_levels_by_guild_id(&self, guild_id: &GuildId) -> Result<Vec<GuildLevel>, RepositoryError>;

    // User Guild membership queries
    async fn find_member_by_id(&self, id: &UserGuildId) -> Result<Option<UserGuild>, RepositoryError>;
    async fn find_members_by_guild_id(&self, guild_id: &GuildId) -> Result<Vec<UserGuild>, RepositoryError>;
    async fn find_guilds_by_user_id(&self, user_id: &crate::models::user::value_object::UserId) -> Result<Vec<UserGuild>, RepositoryError>;
}
