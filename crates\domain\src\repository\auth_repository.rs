use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        password_reset_token::{
            entity::PasswordResetToken,
            value_object::{PasswordResetTokenId, Token},
        },
        role::{entity::Role, value_object::RoleId},
        session::{entity::Session, value_object::SessionId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait AuthRepository: Send + Sync {
    // Password Reset Token operations
    async fn create_password_reset_token(
        &self,
        token: &PasswordResetToken,
    ) -> Result<(), RepositoryError>;
    async fn delete_password_reset_token(
        &self,
        id: &PasswordResetTokenId,
    ) -> Result<(), RepositoryError>;

    // Session operations
    async fn create_session(&self, session: &Session) -> Result<Session, RepositoryError>;
    async fn update_session(&self, session: &Session) -> Result<(), RepositoryError>;
    async fn delete_session(&self, id: &SessionId) -> Result<(), RepositoryError>;

    // Role operations
    async fn create_role(&self, role: &Role) -> Result<Role, RepositoryError>;
    async fn update_role(&self, role: &Role) -> Result<(), RepositoryError>;
    async fn delete_role(&self, id: &RoleId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait AuthReadRepository: Send + Sync {
    // Password Reset Token queries
    async fn find_password_reset_token_by_id(
        &self,
        id: &PasswordResetTokenId,
    ) -> Result<Option<PasswordResetToken>, RepositoryError>;
    async fn find_password_reset_token_by_token(
        &self,
        token: &Token,
    ) -> Result<Option<PasswordResetToken>, RepositoryError>;
    async fn find_password_reset_tokens_by_user(
        &self,
        user_id: &crate::models::user::value_object::UserId,
    ) -> Result<Vec<PasswordResetToken>, RepositoryError>;

    // Session queries
    async fn find_session_by_id(&self, id: &SessionId) -> Result<Option<Session>, RepositoryError>;
    async fn find_sessions_by_user(
        &self,
        user_id: &crate::models::user::value_object::UserId,
    ) -> Result<Vec<Session>, RepositoryError>;
    async fn find_active_sessions(&self) -> Result<Vec<Session>, RepositoryError>;

    // Role queries
    async fn find_role_by_id(&self, id: &RoleId) -> Result<Option<Role>, RepositoryError>;
    async fn find_role_by_name(&self, name: &str) -> Result<Option<Role>, RepositoryError>;
    async fn find_all_roles(&self) -> Result<Vec<Role>, RepositoryError>;
}
