use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        user_market::{entity::UserMarket, value_object::UserMarketId},
        redeem_code::{entity::RedeemCode, value_object::RedeemCodeId},
        wheel_reward::{entity::WheelReward, value_object::WheelRewardId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait MarketRepository: Send + Sync {
    // User Market operations
    async fn create_user_market(&self, market: &UserMarket) -> Result<UserMarket, RepositoryError>;
    async fn update_user_market(&self, market: &UserMarket) -> Result<(), RepositoryError>;
    async fn delete_user_market(&self, id: &UserMarketId) -> Result<(), RepositoryError>;
    
    // Redeem Code operations
    async fn create_redeem_code(&self, code: &RedeemCode) -> Result<RedeemCode, RepositoryError>;
    async fn update_redeem_code(&self, code: &RedeemCode) -> Result<(), RepositoryError>;
    async fn delete_redeem_code(&self, id: &RedeemCodeId) -> Result<(), RepositoryError>;
    
    // Wheel Reward operations
    async fn create_wheel_reward(&self, reward: &WheelReward) -> Result<WheelReward, RepositoryError>;
    async fn update_wheel_reward(&self, reward: &WheelReward) -> Result<(), RepositoryError>;
    async fn delete_wheel_reward(&self, id: &WheelRewardId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait MarketReadRepository: Send + Sync {
    // User Market queries
    async fn find_user_market_by_id(&self, id: &UserMarketId) -> Result<Option<UserMarket>, RepositoryError>;
    async fn find_user_markets_by_user(&self, user_id: &crate::models::user::value_object::UserId) -> Result<Vec<UserMarket>, RepositoryError>;
    async fn find_all_user_markets(&self) -> Result<Vec<UserMarket>, RepositoryError>;
    async fn find_active_user_markets(&self) -> Result<Vec<UserMarket>, RepositoryError>;
    
    // Redeem Code queries
    async fn find_redeem_code_by_id(&self, id: &RedeemCodeId) -> Result<Option<RedeemCode>, RepositoryError>;
    async fn find_redeem_code_by_code(&self, code: &str) -> Result<Option<RedeemCode>, RepositoryError>;
    async fn find_all_redeem_codes(&self) -> Result<Vec<RedeemCode>, RepositoryError>;
    async fn find_active_redeem_codes(&self) -> Result<Vec<RedeemCode>, RepositoryError>;
    
    // Wheel Reward queries
    async fn find_wheel_reward_by_id(&self, id: &WheelRewardId) -> Result<Option<WheelReward>, RepositoryError>;
    async fn find_all_wheel_rewards(&self) -> Result<Vec<WheelReward>, RepositoryError>;
    async fn find_wheel_rewards_by_type(&self, reward_type: &str) -> Result<Vec<WheelReward>, RepositoryError>;
}
