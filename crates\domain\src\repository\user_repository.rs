use crate::{
    error::RepositoryError,
    models::{
        profile::{entity::Profile, value_object::Gender},
        user::{entity::User, profile::UserProfile, value_object::UserId},
        user_achievement::entity::UserAchievement,
        user_boost::entity::UserBoost,
        user_color::entity::UserColor,
        user_currency::entity::UserCurrency,
        user_exp::entity::UserExperience,
        user_faction::entity::UserFaction,
        user_friend::entity::UserFriend,
        user_guild::entity::UserGuild,
        user_item::entity::UserItem,
        user_login::entity::UserLogin,
        user_purchase::entity::UserPurchase,
        user_quest::entity::UserQuest,
        user_redeem::entity::UserRedeem,
        user_report::entity::UserReport,
        user_slot::entity::UserSlot,
        user_stat::entity::UserStat,
        user_title::entity::UserTitle,
    },
};
use async_trait::async_trait;

#[mockall::automock]
#[async_trait]
pub trait UserRepository: Send + Sync + 'static {
    // core
    async fn save(&self, user: &User, gender: &Gender) -> Result<(), RepositoryError>;
    async fn update(&self, user: &User) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &UserId) -> Result<(), RepositoryError>;

    // profile
    async fn update_user_profile(&self, profile: &Profile) -> Result<(), RepositoryError>;
    async fn update_user_color(&self, color: &UserColor) -> Result<(), RepositoryError>;

    // progress
    async fn save_user_exp(&self, exp: &UserExperience) -> Result<(), RepositoryError>;
    async fn update_user_exp(&self, exp: &UserExperience) -> Result<(), RepositoryError>;
    async fn update_user_stat(&self, stat: &UserStat) -> Result<(), RepositoryError>;
    async fn update_user_boost(&self, boost: &UserBoost) -> Result<(), RepositoryError>;

    // inventory
    async fn save_user_item(&self, item: &UserItem) -> Result<(), RepositoryError>;
    async fn update_user_item(&self, item: &UserItem) -> Result<(), RepositoryError>;
    async fn delete_user_item(&self, item: &UserItem) -> Result<(), RepositoryError>;
    async fn save_user_currency(&self, currency: &UserCurrency) -> Result<(), RepositoryError>;
    async fn update_user_currency(&self, currency: &UserCurrency) -> Result<(), RepositoryError>;
    async fn update_user_slot(&self, slot: &UserSlot) -> Result<(), RepositoryError>;

    // social
    async fn save_friend(&self, friend: &UserFriend) -> Result<(), RepositoryError>;
    async fn delete_friend(&self, id: &UserFriend) -> Result<(), RepositoryError>;
    async fn save_user_guild(&self, guild: &UserGuild) -> Result<(), RepositoryError>;
    async fn update_user_guild(&self, guild: &UserGuild) -> Result<(), RepositoryError>;
    async fn delete_user_guild(&self, id: &UserGuild) -> Result<(), RepositoryError>;
    async fn save_user_faction(&self, faction: &UserFaction) -> Result<(), RepositoryError>;
    async fn update_user_faction(&self, faction: &UserFaction) -> Result<(), RepositoryError>;
    async fn delete_user_faction(&self, id: &UserFaction) -> Result<(), RepositoryError>;

    // activity
    async fn save_user_login(&self, login: &UserLogin) -> Result<(), RepositoryError>;
    async fn delete_user_login(&self, id: &UserLogin) -> Result<(), RepositoryError>;
    async fn save_user_purchase(&self, purchase: &UserPurchase) -> Result<(), RepositoryError>;
    async fn delete_user_purchase(&self, id: &UserPurchase) -> Result<(), RepositoryError>;
    async fn save_user_redeem(&self, redeem: &UserRedeem) -> Result<(), RepositoryError>;
    async fn delete_user_redeem(&self, id: &UserRedeem) -> Result<(), RepositoryError>;

    // content
    async fn save_achievement(&self, achievement: &UserAchievement) -> Result<(), RepositoryError>;
    async fn delete_achievement(&self, id: &UserAchievement) -> Result<(), RepositoryError>;
    async fn save_user_quest(&self, quest: &UserQuest) -> Result<(), RepositoryError>;
    async fn update_user_quest(&self, quest: &UserQuest) -> Result<(), RepositoryError>;
    async fn delete_user_quest(&self, id: &UserQuest) -> Result<(), RepositoryError>;
    async fn save_user_title(&self, title: &UserTitle) -> Result<(), RepositoryError>;
    async fn delete_user_title(&self, id: &UserTitle) -> Result<(), RepositoryError>;
    async fn save_user_report(&self, report: &UserReport) -> Result<(), RepositoryError>;
    async fn delete_user_report(&self, id: &UserReport) -> Result<(), RepositoryError>;
}

// ============================================================================
// READ REPOSITORY - QUERIES AND DATA RETRIEVAL
// ============================================================================

/// Read-only operations for user data retrieval
/// This repository handles all query operations across user domains
#[mockall::automock]
#[async_trait]
pub trait UserReadRepository: Send + Sync {
    // Core user queries
    async fn get_latest_id(&self) -> Result<UserId, RepositoryError>;
    async fn find_by_id(
        &self,
        id: &UserId,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError>;
    async fn find_by_username(
        &self,
        username: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError>;
    async fn find_by_email(
        &self,
        email: &str,
    ) -> Result<Option<(User, Option<Profile>)>, RepositoryError>;
    async fn get_with_profile(&self, id: &UserId) -> Result<Option<UserProfile>, RepositoryError>;

    // Additional query methods can be added here for specific use cases
    // For example:
    // async fn find_users_by_guild(&self, guild_id: &GuildId) -> Result<Vec<User>, RepositoryError>;
    // async fn find_users_by_faction(&self, faction_id: &FactionId) -> Result<Vec<User>, RepositoryError>;
}
