use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        item::{entity::Item, value_object::ItemId},
        item_bundle::{entity::ItemBundle, value_object::ItemBundleId},
        item_effect::{entity::ItemEffect, value_object::ItemEffectId},
        item_lottery::{entity::ItemLottery, value_object::ItemLotteryId},
        item_rarity::{entity::ItemRarity, value_object::ItemRarityId},
        item_requirement::{entity::ItemRequirement, value_object::ItemRequirementId},
        item_skill::{entity::ItemSkill, value_object::ItemSkillId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait ItemRepository: Send + Sync {
    // Item CRUD operations
    async fn create(&self, item: &Item) -> Result<Item, RepositoryError>;
    async fn update(&self, item: &Item) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ItemId) -> Result<(), RepositoryError>;

    // Item Bundle operations
    async fn create_bundle(&self, bundle: &ItemBundle) -> Result<ItemBundle, RepositoryError>;
    async fn update_bundle(&self, bundle: &ItemBundle) -> Result<(), RepositoryError>;
    async fn delete_bundle(&self, id: &ItemBundleId) -> Result<(), RepositoryError>;

    // Item Effect operations
    async fn create_effect(&self, effect: &ItemEffect) -> Result<ItemEffect, RepositoryError>;
    async fn update_effect(&self, effect: &ItemEffect) -> Result<(), RepositoryError>;
    async fn delete_effect(&self, id: &ItemEffectId) -> Result<(), RepositoryError>;

    // Item Lottery operations
    async fn create_lottery(&self, lottery: &ItemLottery) -> Result<ItemLottery, RepositoryError>;
    async fn update_lottery(&self, lottery: &ItemLottery) -> Result<(), RepositoryError>;
    async fn delete_lottery(&self, id: &ItemLotteryId) -> Result<(), RepositoryError>;

    // Item Rarity operations
    async fn create_rarity(&self, rarity: &ItemRarity) -> Result<ItemRarity, RepositoryError>;
    async fn update_rarity(&self, rarity: &ItemRarity) -> Result<(), RepositoryError>;
    async fn delete_rarity(&self, id: &ItemRarityId) -> Result<(), RepositoryError>;

    // Item Requirement operations
    async fn create_requirement(&self, requirement: &ItemRequirement) -> Result<ItemRequirement, RepositoryError>;
    async fn update_requirement(&self, requirement: &ItemRequirement) -> Result<(), RepositoryError>;
    async fn delete_requirement(&self, id: &ItemRequirementId) -> Result<(), RepositoryError>;

    // Item Skill operations
    async fn create_skill(&self, skill: &ItemSkill) -> Result<ItemSkill, RepositoryError>;
    async fn update_skill(&self, skill: &ItemSkill) -> Result<(), RepositoryError>;
    async fn delete_skill(&self, id: &ItemSkillId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait ItemReadRepository: Send + Sync {
    // Item queries
    async fn find_by_id(&self, id: &ItemId) -> Result<Option<Item>, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Item>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Item>, RepositoryError>;
    async fn find_paginate(&self, page: u32, limit: u32) -> Result<Vec<Item>, RepositoryError>;

    // Item Bundle queries
    async fn find_bundle_by_id(&self, id: &ItemBundleId) -> Result<Option<ItemBundle>, RepositoryError>;
    async fn find_bundles_by_item_id(&self, item_id: &ItemId) -> Result<Vec<ItemBundle>, RepositoryError>;

    // Item Effect queries
    async fn find_effect_by_id(&self, id: &ItemEffectId) -> Result<Option<ItemEffect>, RepositoryError>;
    async fn find_effects_by_item_id(&self, item_id: &ItemId) -> Result<Vec<ItemEffect>, RepositoryError>;

    // Item Lottery queries
    async fn find_lottery_by_id(&self, id: &ItemLotteryId) -> Result<Option<ItemLottery>, RepositoryError>;
    async fn find_lotteries_by_item_id(&self, item_id: &ItemId) -> Result<Vec<ItemLottery>, RepositoryError>;

    // Item Rarity queries
    async fn find_rarity_by_id(&self, id: &ItemRarityId) -> Result<Option<ItemRarity>, RepositoryError>;
    async fn find_all_rarities(&self) -> Result<Vec<ItemRarity>, RepositoryError>;

    // Item Requirement queries
    async fn find_requirement_by_id(&self, id: &ItemRequirementId) -> Result<Option<ItemRequirement>, RepositoryError>;
    async fn find_requirements_by_item_id(&self, item_id: &ItemId) -> Result<Vec<ItemRequirement>, RepositoryError>;

    // Item Skill queries
    async fn find_skill_by_id(&self, id: &ItemSkillId) -> Result<Option<ItemSkill>, RepositoryError>;
    async fn find_skills_by_item_id(&self, item_id: &ItemId) -> Result<Vec<ItemSkill>, RepositoryError>;
}
