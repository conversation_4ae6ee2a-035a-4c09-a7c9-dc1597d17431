use crate::SeaORMErr;
use crate::models::password_reset_tokens::ActiveModel as PasswordResetTokensActiveModel;
use crate::models::password_reset_tokens::Entity as PasswordResetTokenModel;
use async_trait::async_trait;
use reforged_domain::repository::auth_repository::AuthRepository;
use reforged_domain::{
    error::RepositoryError,
    models::{
        password_reset_token::{entity::PasswordResetToken, value_object::PasswordResetTokenId},
        role::{entity::Role, value_object::RoleId},
        session::{entity::Session, value_object::SessionId},
    },
};
use reforged_shared::{IdTrait, Value};
use sea_orm::EntityTrait;
use sea_orm::{ActiveModelTrait, ActiveValue::Set, DatabaseConnection};

pub struct PostgresAuthRepository {
    pool: DatabaseConnection,
}

impl PostgresAuthRepository {
    fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl AuthRepository for PostgresAuthRepository {
    // Password Reset Token operations
    async fn create_password_reset_token(
        &self,
        token: &PasswordResetToken,
    ) -> Result<(), RepositoryError> {
        let new_token = PasswordResetTokensActiveModel {
            id: Set(token.id().get_id()),
            user_id: Set(token.user_id().get_id()),
            token: Set(token.token().value()),
            created_at: Set(token.created_at().value().naive_utc()),
            expires_at: Set(token.expires_at().value().naive_utc()),
        };

        new_token
            .insert(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    async fn delete_password_reset_token(
        &self,
        id: &PasswordResetTokenId,
    ) -> Result<(), RepositoryError> {
        PasswordResetTokenModel::delete_by_id(id.get_id())
            .exec(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        Ok(())
    }

    // Session operations
    async fn create_session(&self, session: &Session) -> Result<Session, RepositoryError> {
        todo!()
    }

    async fn update_session(&self, session: &Session) -> Result<(), RepositoryError> {
        todo!()
    }

    async fn delete_session(&self, id: &SessionId) -> Result<(), RepositoryError> {
        todo!()
    }

    // Role operations
    async fn create_role(&self, role: &Role) -> Result<Role, RepositoryError> {
        todo!()
    }

    async fn update_role(&self, role: &Role) -> Result<(), RepositoryError> {
        todo!()
    }

    async fn delete_role(&self, id: &RoleId) -> Result<(), RepositoryError> {
        todo!()
    }
}
