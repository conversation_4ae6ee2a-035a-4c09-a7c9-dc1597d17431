use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        game_rate_setting::{entity::GameRateSetting, value_object::GameRateSettingId},
        game_setting::{entity::GameSetting, value_object::GameSettingId},
        profanity_filter_setting::{entity::ProfanityFilterSettings, value_object::ProfanityFilterSettingsId},
        server::{entity::Server, value_object::ServerId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait SystemRepository: Send + Sync {
    // Game Rate Setting operations
    async fn create_game_rate_setting(&self, setting: &GameRateSetting) -> Result<GameRateSetting, RepositoryError>;
    async fn update_game_rate_setting(&self, setting: &GameRateSetting) -> Result<(), RepositoryError>;
    async fn delete_game_rate_setting(&self, id: &GameRateSettingId) -> Result<(), RepositoryError>;
    
    // Game Setting operations
    async fn create_game_setting(&self, setting: &GameSetting) -> Result<GameSetting, RepositoryError>;
    async fn update_game_setting(&self, setting: &GameSetting) -> Result<(), RepositoryError>;
    async fn delete_game_setting(&self, id: &GameSettingId) -> Result<(), RepositoryError>;
    
    // Profanity Filter Setting operations
    async fn create_profanity_filter_setting(&self, setting: &ProfanityFilterSettings) -> Result<ProfanityFilterSettings, RepositoryError>;
    async fn update_profanity_filter_setting(&self, setting: &ProfanityFilterSettings) -> Result<(), RepositoryError>;
    async fn delete_profanity_filter_setting(&self, id: &ProfanityFilterSettingsId) -> Result<(), RepositoryError>;
    
    // Server operations
    async fn create_server(&self, server: &Server) -> Result<Server, RepositoryError>;
    async fn update_server(&self, server: &Server) -> Result<(), RepositoryError>;
    async fn delete_server(&self, id: &ServerId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait SystemReadRepository: Send + Sync {
    // Game Rate Setting queries
    async fn find_game_rate_setting_by_id(&self, id: &GameRateSettingId) -> Result<Option<GameRateSetting>, RepositoryError>;
    async fn find_all_game_rate_settings(&self) -> Result<Vec<GameRateSetting>, RepositoryError>;
    
    // Game Setting queries
    async fn find_game_setting_by_id(&self, id: &GameSettingId) -> Result<Option<GameSetting>, RepositoryError>;
    async fn find_game_setting_by_key(&self, key: &str) -> Result<Option<GameSetting>, RepositoryError>;
    async fn find_all_game_settings(&self) -> Result<Vec<GameSetting>, RepositoryError>;
    
    // Profanity Filter Setting queries
    async fn find_profanity_filter_setting_by_id(&self, id: &ProfanityFilterSettingsId) -> Result<Option<ProfanityFilterSettings>, RepositoryError>;
    async fn find_all_profanity_filter_settings(&self) -> Result<Vec<ProfanityFilterSettings>, RepositoryError>;
    
    // Server queries
    async fn find_server_by_id(&self, id: &ServerId) -> Result<Option<Server>, RepositoryError>;
    async fn find_server_by_name(&self, name: &str) -> Result<Option<Server>, RepositoryError>;
    async fn find_all_servers(&self) -> Result<Vec<Server>, RepositoryError>;
    async fn find_active_servers(&self) -> Result<Vec<Server>, RepositoryError>;
}
