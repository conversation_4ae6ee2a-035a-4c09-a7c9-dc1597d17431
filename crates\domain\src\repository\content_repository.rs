use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        achievement::{entity::Achievement, value_object::AchievementId},
        book::{entity::Book, value_object::BookId},
        class::{entity::Class, value_object::ClassId},
        class_category::{entity::ClassCategory, value_object::ClassCategoryId},
        class_skill::{entity::ClassSkill, value_object::ClassSkillId},
        faction::{entity::Faction, value_object::FactionId},
        quest::{entity::Quest, value_object::QuestId},
        skill::{entity::Skill, value_object::SkillId},
        title::{entity::Title, value_object::TitleId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait ContentRepository: Send + Sync {
    // Achievement operations
    async fn create_achievement(&self, achievement: &Achievement) -> Result<Achievement, RepositoryError>;
    async fn update_achievement(&self, achievement: &Achievement) -> Result<(), RepositoryError>;
    async fn delete_achievement(&self, id: &AchievementId) -> Result<(), RepositoryError>;

    // Book operations
    async fn create_book(&self, book: &Book) -> Result<Book, RepositoryError>;
    async fn update_book(&self, book: &Book) -> Result<(), RepositoryError>;
    async fn delete_book(&self, id: &BookId) -> Result<(), RepositoryError>;

    // Class operations
    async fn create_class(&self, class: &Class) -> Result<Class, RepositoryError>;
    async fn update_class(&self, class: &Class) -> Result<(), RepositoryError>;
    async fn delete_class(&self, id: &ClassId) -> Result<(), RepositoryError>;

    // Class Category operations
    async fn create_class_category(&self, category: &ClassCategory) -> Result<ClassCategory, RepositoryError>;
    async fn update_class_category(&self, category: &ClassCategory) -> Result<(), RepositoryError>;
    async fn delete_class_category(&self, id: &ClassCategoryId) -> Result<(), RepositoryError>;

    // Class Skill operations
    async fn create_class_skill(&self, skill: &ClassSkill) -> Result<ClassSkill, RepositoryError>;
    async fn update_class_skill(&self, skill: &ClassSkill) -> Result<(), RepositoryError>;
    async fn delete_class_skill(&self, id: &ClassSkillId) -> Result<(), RepositoryError>;

    // Faction operations
    async fn create_faction(&self, faction: &Faction) -> Result<Faction, RepositoryError>;
    async fn update_faction(&self, faction: &Faction) -> Result<(), RepositoryError>;
    async fn delete_faction(&self, id: &FactionId) -> Result<(), RepositoryError>;

    // Quest operations
    async fn create_quest(&self, quest: &Quest) -> Result<Quest, RepositoryError>;
    async fn update_quest(&self, quest: &Quest) -> Result<(), RepositoryError>;
    async fn delete_quest(&self, id: &QuestId) -> Result<(), RepositoryError>;

    // Skill operations
    async fn create_skill(&self, skill: &Skill) -> Result<Skill, RepositoryError>;
    async fn update_skill(&self, skill: &Skill) -> Result<(), RepositoryError>;
    async fn delete_skill(&self, id: &SkillId) -> Result<(), RepositoryError>;

    // Title operations
    async fn create_title(&self, title: &Title) -> Result<Title, RepositoryError>;
    async fn update_title(&self, title: &Title) -> Result<(), RepositoryError>;
    async fn delete_title(&self, id: &TitleId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait ContentReadRepository: Send + Sync {
    // Achievement queries
    async fn find_achievement_by_id(&self, id: &AchievementId) -> Result<Option<Achievement>, RepositoryError>;
    async fn find_achievements_by_name(&self, name: &str) -> Result<Vec<Achievement>, RepositoryError>;
    async fn find_all_achievements(&self) -> Result<Vec<Achievement>, RepositoryError>;

    // Book queries
    async fn find_book_by_id(&self, id: &BookId) -> Result<Option<Book>, RepositoryError>;
    async fn find_books_by_name(&self, name: &str) -> Result<Vec<Book>, RepositoryError>;
    async fn find_all_books(&self) -> Result<Vec<Book>, RepositoryError>;

    // Class queries
    async fn find_class_by_id(&self, id: &ClassId) -> Result<Option<Class>, RepositoryError>;
    async fn find_classes_by_name(&self, name: &str) -> Result<Vec<Class>, RepositoryError>;
    async fn find_classes_by_category(&self, category_id: &ClassCategoryId) -> Result<Vec<Class>, RepositoryError>;
    async fn find_all_classes(&self) -> Result<Vec<Class>, RepositoryError>;

    // Class Category queries
    async fn find_class_category_by_id(&self, id: &ClassCategoryId) -> Result<Option<ClassCategory>, RepositoryError>;
    async fn find_class_category_by_name(&self, name: &str) -> Result<Option<ClassCategory>, RepositoryError>;
    async fn find_all_class_categories(&self) -> Result<Vec<ClassCategory>, RepositoryError>;

    // Class Skill queries
    async fn find_class_skill_by_id(&self, id: &ClassSkillId) -> Result<Option<ClassSkill>, RepositoryError>;
    async fn find_class_skills_by_class(&self, class_id: &ClassId) -> Result<Vec<ClassSkill>, RepositoryError>;
    async fn find_class_skills_by_skill(&self, skill_id: &SkillId) -> Result<Vec<ClassSkill>, RepositoryError>;

    // Faction queries
    async fn find_faction_by_id(&self, id: &FactionId) -> Result<Option<Faction>, RepositoryError>;
    async fn find_faction_by_name(&self, name: &str) -> Result<Option<Faction>, RepositoryError>;
    async fn find_all_factions(&self) -> Result<Vec<Faction>, RepositoryError>;

    // Quest queries
    async fn find_quest_by_id(&self, id: &QuestId) -> Result<Option<Quest>, RepositoryError>;
    async fn find_quests_by_name(&self, name: &str) -> Result<Vec<Quest>, RepositoryError>;
    async fn find_all_quests(&self) -> Result<Vec<Quest>, RepositoryError>;

    // Skill queries
    async fn find_skill_by_id(&self, id: &SkillId) -> Result<Option<Skill>, RepositoryError>;
    async fn find_skill_by_name(&self, name: &str) -> Result<Option<Skill>, RepositoryError>;
    async fn find_all_skills(&self) -> Result<Vec<Skill>, RepositoryError>;

    // Title queries
    async fn find_title_by_id(&self, id: &TitleId) -> Result<Option<Title>, RepositoryError>;
    async fn find_title_by_name(&self, name: &str) -> Result<Option<Title>, RepositoryError>;
    async fn find_all_titles(&self) -> Result<Vec<Title>, RepositoryError>;
}