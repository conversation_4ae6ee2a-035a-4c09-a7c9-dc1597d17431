use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        discord_command::{entity::DiscordCommand, value_object::DiscordCommandId},
        store::{entity::Store, value_object::StoreId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait BotRepository: Send + Sync {
    // Discord Command operations
    async fn create_discord_command(&self, command: &DiscordCommand) -> Result<DiscordCommand, RepositoryError>;
    async fn update_discord_command(&self, command: &DiscordCommand) -> Result<(), RepositoryError>;
    async fn delete_discord_command(&self, id: &DiscordCommandId) -> Result<(), RepositoryError>;
    
    // Store operations
    async fn create_store(&self, store: &Store) -> Result<Store, RepositoryError>;
    async fn update_store(&self, store: &Store) -> Result<(), RepositoryError>;
    async fn delete_store(&self, id: &StoreId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait BotReadRepository: Send + Sync {
    // Discord Command queries
    async fn find_discord_command_by_id(&self, id: &DiscordCommandId) -> Result<Option<DiscordCommand>, RepositoryError>;
    async fn find_discord_commands_by_name(&self, name: &str) -> Result<Vec<DiscordCommand>, RepositoryError>;
    async fn find_all_discord_commands(&self) -> Result<Vec<DiscordCommand>, RepositoryError>;
    async fn find_active_discord_commands(&self) -> Result<Vec<DiscordCommand>, RepositoryError>;
    
    // Store queries
    async fn find_store_by_id(&self, id: &StoreId) -> Result<Option<Store>, RepositoryError>;
    async fn find_stores_by_name(&self, name: &str) -> Result<Vec<Store>, RepositoryError>;
    async fn find_all_stores(&self) -> Result<Vec<Store>, RepositoryError>;
    async fn find_active_stores(&self) -> Result<Vec<Store>, RepositoryError>;
}
