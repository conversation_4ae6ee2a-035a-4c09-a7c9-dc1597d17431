use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        shop::{entity::Shop, value_object::ShopId},
        shop_item::{entity::ShopItem, value_object::ShopItemId},
        shop_location::{entity::ShopLocation, value_object::ShopLocationId},
        hair_shop::{entity::HairShop, value_object::HairShopId},
        hair_shop_item::{entity::HairShopItem, value_object::HairShopItemId},
        hair::{entity::Hair, value_object::HairId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait ShopRepository: Send + Sync {
    // Shop operations
    async fn create(&self, shop: &Shop) -> Result<Shop, RepositoryError>;
    async fn update(&self, shop: &Shop) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ShopId) -> Result<(), RepositoryError>;

    // Shop Item operations
    async fn create_item(&self, item: &ShopItem) -> Result<ShopItem, RepositoryError>;
    async fn update_item(&self, item: &ShopItem) -> Result<(), RepositoryError>;
    async fn delete_item(&self, id: &ShopItemId) -> Result<(), RepositoryError>;

    // Shop Location operations
    async fn create_location(&self, location: &ShopLocation) -> Result<ShopLocation, RepositoryError>;
    async fn update_location(&self, location: &ShopLocation) -> Result<(), RepositoryError>;
    async fn delete_location(&self, id: &ShopLocationId) -> Result<(), RepositoryError>;

    // Hair Shop operations
    async fn create_hair_shop(&self, shop: &HairShop) -> Result<HairShop, RepositoryError>;
    async fn update_hair_shop(&self, shop: &HairShop) -> Result<(), RepositoryError>;
    async fn delete_hair_shop(&self, id: &HairShopId) -> Result<(), RepositoryError>;

    // Hair Shop Item operations
    async fn create_hair_shop_item(&self, item: &HairShopItem) -> Result<HairShopItem, RepositoryError>;
    async fn update_hair_shop_item(&self, item: &HairShopItem) -> Result<(), RepositoryError>;
    async fn delete_hair_shop_item(&self, id: &HairShopItemId) -> Result<(), RepositoryError>;

    // Hair operations
    async fn create_hair(&self, hair: &Hair) -> Result<Hair, RepositoryError>;
    async fn update_hair(&self, hair: &Hair) -> Result<(), RepositoryError>;
    async fn delete_hair(&self, id: &HairId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait ShopReadRepository: Send + Sync {
    // Shop queries
    async fn find_by_id(&self, id: &ShopId) -> Result<Option<Shop>, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<Shop>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Shop>, RepositoryError>;
    async fn find_paginate(&self, page: u32, limit: u32) -> Result<Vec<Shop>, RepositoryError>;

    // Shop Item queries
    async fn find_item_by_id(&self, id: &ShopItemId) -> Result<Option<ShopItem>, RepositoryError>;
    async fn find_items_by_shop(&self, shop_id: &ShopId) -> Result<Vec<ShopItem>, RepositoryError>;

    // Shop Location queries
    async fn find_location_by_id(&self, id: &ShopLocationId) -> Result<Option<ShopLocation>, RepositoryError>;
    async fn find_locations_by_shop(&self, shop_id: &ShopId) -> Result<Vec<ShopLocation>, RepositoryError>;

    // Hair Shop queries
    async fn find_hair_shop_by_id(&self, id: &HairShopId) -> Result<Option<HairShop>, RepositoryError>;
    async fn find_hair_shop_by_name(&self, name: &str) -> Result<Option<HairShop>, RepositoryError>;
    async fn find_all_hair_shops(&self) -> Result<Vec<HairShop>, RepositoryError>;

    // Hair Shop Item queries
    async fn find_hair_shop_item_by_id(&self, id: &HairShopItemId) -> Result<Option<HairShopItem>, RepositoryError>;
    async fn find_hair_shop_items_by_shop(&self, shop_id: &HairShopId) -> Result<Vec<HairShopItem>, RepositoryError>;

    // Hair queries
    async fn find_hair_by_id(&self, id: &HairId) -> Result<Option<Hair>, RepositoryError>;
    async fn find_hair_by_name(&self, name: &str) -> Result<Option<Hair>, RepositoryError>;
    async fn find_all_hair(&self) -> Result<Vec<Hair>, RepositoryError>;
}
