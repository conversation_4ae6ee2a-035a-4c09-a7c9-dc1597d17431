use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        admin_upload::{entity::AdminUpload, value_object::AdminUploadId},
        deleted_user_item::{entity::DeletedUserItem, value_object::DeletedUserItemId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait AdminRepository: Send + Sync {
    // Admin Upload operations
    async fn create_admin_upload(&self, upload: &AdminUpload) -> Result<AdminUpload, RepositoryError>;
    async fn update_admin_upload(&self, upload: &AdminUpload) -> Result<(), RepositoryError>;
    async fn delete_admin_upload(&self, id: &AdminUploadId) -> Result<(), RepositoryError>;
    
    // Deleted User Item operations
    async fn create_deleted_user_item(&self, item: &DeletedUserItem) -> Result<DeletedUserItem, RepositoryError>;
    async fn update_deleted_user_item(&self, item: &DeletedUserItem) -> Result<(), RepositoryError>;
    async fn delete_deleted_user_item(&self, id: &DeletedUserItemId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait AdminReadRepository: Send + Sync {
    // Admin Upload queries
    async fn find_admin_upload_by_id(&self, id: &AdminUploadId) -> Result<Option<AdminUpload>, RepositoryError>;
    async fn find_admin_uploads_by_user(&self, user_id: &crate::models::user::value_object::UserId) -> Result<Vec<AdminUpload>, RepositoryError>;
    async fn find_all_admin_uploads(&self) -> Result<Vec<AdminUpload>, RepositoryError>;
    async fn find_admin_uploads_paginate(&self, page: u32, limit: u32) -> Result<Vec<AdminUpload>, RepositoryError>;
    
    // Deleted User Item queries
    async fn find_deleted_user_item_by_id(&self, id: &DeletedUserItemId) -> Result<Option<DeletedUserItem>, RepositoryError>;
    async fn find_deleted_user_items_by_user(&self, user_id: &crate::models::user::value_object::UserId) -> Result<Vec<DeletedUserItem>, RepositoryError>;
    async fn find_all_deleted_user_items(&self) -> Result<Vec<DeletedUserItem>, RepositoryError>;
    async fn find_deleted_user_items_paginate(&self, page: u32, limit: u32) -> Result<Vec<DeletedUserItem>, RepositoryError>;
}
