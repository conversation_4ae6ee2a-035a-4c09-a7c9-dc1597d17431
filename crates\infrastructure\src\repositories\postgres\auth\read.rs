use crate::SeaORMErr;
use crate::models::password_reset_tokens::Column as PasswordResetTokenColumn;
use crate::models::password_reset_tokens::Entity as PasswordResetTokenModel;
use async_trait::async_trait;
use reforged_domain::{
    error::RepositoryError,
    models::{
        password_reset_token::{
            entity::PasswordResetToken,
            value_object::{PasswordResetTokenId, Token},
        },
        role::{entity::Role, value_object::RoleId},
        session::{entity::Session, value_object::SessionId},
        user::value_object::UserId,
    },
    repository::auth_repository::AuthReadRepository,
};
use reforged_shared::{IdTrait, Value};
use sea_orm::ColumnTrait;
use sea_orm::QueryFilter;
use sea_orm::{DatabaseConnection, EntityTrait};

use crate::mappers::password_reset_token_mapper::PasswordResetDbModelTokenMapper;

pub struct PostgresAuthReadRepository {
    pool: DatabaseConnection,
}

impl PostgresAuthReadRepository {
    pub fn new(pool: DatabaseConnection) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl AuthReadRepository for PostgresAuthReadRepository {
    async fn find_password_reset_token_by_id(
        &self,
        id: &PasswordResetTokenId,
    ) -> Result<Option<PasswordResetToken>, RepositoryError> {
        let token = PasswordResetTokenModel::find_by_id(id.get_id())
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        token
            .ok_or(RepositoryError::NotFound(format!(
                "with id: {}",
                id.get_id()
            )))
            .and_then(|token| {
                let mapped_token = PasswordResetDbModelTokenMapper::new(token);
                let token = PasswordResetToken::from(mapped_token);

                Ok(Some(token))
            })
    }

    async fn find_password_reset_token_by_token(
        &self,
        token: &Token,
    ) -> Result<Option<PasswordResetToken>, RepositoryError> {
        let model = PasswordResetTokenModel::find()
            .filter(PasswordResetTokenColumn::Token.eq(token.value()))
            .one(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        model
            .ok_or(RepositoryError::NotFound(format!(
                "with token: {}",
                token.value()
            )))
            .and_then(|token| {
                let mapped_token = PasswordResetDbModelTokenMapper::new(token);
                let token = PasswordResetToken::from(mapped_token);

                Ok(Some(token))
            })
    }

    async fn find_password_reset_tokens_by_user(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<PasswordResetToken>, RepositoryError> {
        let model = PasswordResetTokenModel::find()
            .filter(PasswordResetTokenColumn::UserId.eq(user_id.get_id()))
            .all(&self.pool)
            .await
            .map_err(SeaORMErr::from)?;

        let tokens = model
            .into_iter()
            .map(|token| {
                let mapped_token = PasswordResetDbModelTokenMapper::new(token);
                let token = PasswordResetToken::from(mapped_token);

                token
            })
            .collect::<Vec<_>>();

        Ok(tokens)
    }

    // Session queries
    async fn find_session_by_id(&self, id: &SessionId) -> Result<Option<Session>, RepositoryError> {
        todo!()
    }

    async fn find_sessions_by_user(
        &self,
        user_id: &UserId,
    ) -> Result<Vec<Session>, RepositoryError> {
        todo!()
    }

    async fn find_active_sessions(&self) -> Result<Vec<Session>, RepositoryError> {
        todo!()
    }

    // Role queries
    async fn find_role_by_id(&self, id: &RoleId) -> Result<Option<Role>, RepositoryError> {
        todo!()
    }

    async fn find_role_by_name(&self, name: &str) -> Result<Option<Role>, RepositoryError> {
        todo!()
    }

    async fn find_all_roles(&self) -> Result<Vec<Role>, RepositoryError> {
        todo!()
    }
}
