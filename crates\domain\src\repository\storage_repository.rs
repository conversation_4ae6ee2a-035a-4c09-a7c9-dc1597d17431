use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        file::{entity::File, value_object::FileId},
        directory::{entity::Directory, value_object::DirectoryId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait StorageRepository: Send + Sync {
    // File operations
    async fn create_file(&self, file: &File) -> Result<File, RepositoryError>;
    async fn update_file(&self, file: &File) -> Result<(), RepositoryError>;
    async fn delete_file(&self, id: &FileId) -> Result<(), RepositoryError>;
    
    // Directory operations
    async fn create_directory(&self, directory: &Directory) -> Result<Directory, RepositoryError>;
    async fn update_directory(&self, directory: &Directory) -> Result<(), RepositoryError>;
    async fn delete_directory(&self, id: &DirectoryId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait StorageReadRepository: Send + Sync {
    // File queries
    async fn find_file_by_id(&self, id: &FileId) -> Result<Option<File>, RepositoryError>;
    async fn find_files_by_name(&self, name: &str) -> Result<Vec<File>, RepositoryError>;
    async fn find_files_by_directory(&self, directory_id: &DirectoryId) -> Result<Vec<File>, RepositoryError>;
    async fn find_all_files(&self) -> Result<Vec<File>, RepositoryError>;
    
    // Directory queries
    async fn find_directory_by_id(&self, id: &DirectoryId) -> Result<Option<Directory>, RepositoryError>;
    async fn find_directories_by_name(&self, name: &str) -> Result<Vec<Directory>, RepositoryError>;
    async fn find_directories_by_parent(&self, parent_id: &DirectoryId) -> Result<Vec<Directory>, RepositoryError>;
    async fn find_root_directories(&self) -> Result<Vec<Directory>, RepositoryError>;
    async fn find_all_directories(&self) -> Result<Vec<Directory>, RepositoryError>;
}
