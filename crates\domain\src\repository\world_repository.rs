use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        map::{entity::Map, value_object::MapId},
        map_cell::{entity::MapCell, value_object::MapCellId},
        map_item::{entity::MapItem, value_object::MapItemId},
        map_monster::{entity::MapMonster, value_object::MapMonsterId},
        monster::{entity::Monster, value_object::MonsterId},
        monster_boss::{entity::MonsterBoss, value_object::MonsterBossId},
        monster_drop::{entity::MonsterDrop, value_object::MonsterDropId},
        monster_skill::{entity::MonsterSkill, value_object::MonsterSkillId},
        quest_location::{entity::QuestLocation, value_object::QuestLocationId},
        quest_requirement::{entity::QuestRequirement, value_object::QuestRequirementId},
        quest_reward::{entity::QuestReward, value_object::QuestRewardId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait WorldRepository: Send + Sync {
    // Map operations
    async fn create_map(&self, map: &Map) -> Result<Map, RepositoryError>;
    async fn update_map(&self, map: &Map) -> Result<(), RepositoryError>;
    async fn delete_map(&self, id: &MapId) -> Result<(), RepositoryError>;
    
    // Map Cell operations
    async fn create_map_cell(&self, cell: &MapCell) -> Result<MapCell, RepositoryError>;
    async fn update_map_cell(&self, cell: &MapCell) -> Result<(), RepositoryError>;
    async fn delete_map_cell(&self, id: &MapCellId) -> Result<(), RepositoryError>;
    
    // Map Item operations
    async fn create_map_item(&self, item: &MapItem) -> Result<MapItem, RepositoryError>;
    async fn update_map_item(&self, item: &MapItem) -> Result<(), RepositoryError>;
    async fn delete_map_item(&self, id: &MapItemId) -> Result<(), RepositoryError>;
    
    // Map Monster operations
    async fn create_map_monster(&self, monster: &MapMonster) -> Result<MapMonster, RepositoryError>;
    async fn update_map_monster(&self, monster: &MapMonster) -> Result<(), RepositoryError>;
    async fn delete_map_monster(&self, id: &MapMonsterId) -> Result<(), RepositoryError>;
    
    // Monster operations
    async fn create_monster(&self, monster: &Monster) -> Result<Monster, RepositoryError>;
    async fn update_monster(&self, monster: &Monster) -> Result<(), RepositoryError>;
    async fn delete_monster(&self, id: &MonsterId) -> Result<(), RepositoryError>;
    
    // Monster Boss operations
    async fn create_monster_boss(&self, boss: &MonsterBoss) -> Result<MonsterBoss, RepositoryError>;
    async fn update_monster_boss(&self, boss: &MonsterBoss) -> Result<(), RepositoryError>;
    async fn delete_monster_boss(&self, id: &MonsterBossId) -> Result<(), RepositoryError>;
    
    // Monster Drop operations
    async fn create_monster_drop(&self, drop: &MonsterDrop) -> Result<MonsterDrop, RepositoryError>;
    async fn update_monster_drop(&self, drop: &MonsterDrop) -> Result<(), RepositoryError>;
    async fn delete_monster_drop(&self, id: &MonsterDropId) -> Result<(), RepositoryError>;
    
    // Monster Skill operations
    async fn create_monster_skill(&self, skill: &MonsterSkill) -> Result<MonsterSkill, RepositoryError>;
    async fn update_monster_skill(&self, skill: &MonsterSkill) -> Result<(), RepositoryError>;
    async fn delete_monster_skill(&self, id: &MonsterSkillId) -> Result<(), RepositoryError>;
    
    // Quest Location operations
    async fn create_quest_location(&self, location: &QuestLocation) -> Result<QuestLocation, RepositoryError>;
    async fn update_quest_location(&self, location: &QuestLocation) -> Result<(), RepositoryError>;
    async fn delete_quest_location(&self, id: &QuestLocationId) -> Result<(), RepositoryError>;
    
    // Quest Requirement operations
    async fn create_quest_requirement(&self, requirement: &QuestRequirement) -> Result<QuestRequirement, RepositoryError>;
    async fn update_quest_requirement(&self, requirement: &QuestRequirement) -> Result<(), RepositoryError>;
    async fn delete_quest_requirement(&self, id: &QuestRequirementId) -> Result<(), RepositoryError>;
    
    // Quest Reward operations
    async fn create_quest_reward(&self, reward: &QuestReward) -> Result<QuestReward, RepositoryError>;
    async fn update_quest_reward(&self, reward: &QuestReward) -> Result<(), RepositoryError>;
    async fn delete_quest_reward(&self, id: &QuestRewardId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait WorldReadRepository: Send + Sync {
    // Map queries
    async fn find_map_by_id(&self, id: &MapId) -> Result<Option<Map>, RepositoryError>;
    async fn find_maps_by_name(&self, name: &str) -> Result<Vec<Map>, RepositoryError>;
    async fn find_all_maps(&self) -> Result<Vec<Map>, RepositoryError>;
    
    // Map Cell queries
    async fn find_map_cell_by_id(&self, id: &MapCellId) -> Result<Option<MapCell>, RepositoryError>;
    async fn find_map_cells_by_map(&self, map_id: &MapId) -> Result<Vec<MapCell>, RepositoryError>;
    
    // Map Item queries
    async fn find_map_item_by_id(&self, id: &MapItemId) -> Result<Option<MapItem>, RepositoryError>;
    async fn find_map_items_by_map(&self, map_id: &MapId) -> Result<Vec<MapItem>, RepositoryError>;
    
    // Map Monster queries
    async fn find_map_monster_by_id(&self, id: &MapMonsterId) -> Result<Option<MapMonster>, RepositoryError>;
    async fn find_map_monsters_by_map(&self, map_id: &MapId) -> Result<Vec<MapMonster>, RepositoryError>;
    
    // Monster queries
    async fn find_monster_by_id(&self, id: &MonsterId) -> Result<Option<Monster>, RepositoryError>;
    async fn find_monsters_by_name(&self, name: &str) -> Result<Vec<Monster>, RepositoryError>;
    async fn find_all_monsters(&self) -> Result<Vec<Monster>, RepositoryError>;
    
    // Monster Boss queries
    async fn find_monster_boss_by_id(&self, id: &MonsterBossId) -> Result<Option<MonsterBoss>, RepositoryError>;
    async fn find_monster_bosses_by_monster(&self, monster_id: &MonsterId) -> Result<Vec<MonsterBoss>, RepositoryError>;
    
    // Monster Drop queries
    async fn find_monster_drop_by_id(&self, id: &MonsterDropId) -> Result<Option<MonsterDrop>, RepositoryError>;
    async fn find_monster_drops_by_monster(&self, monster_id: &MonsterId) -> Result<Vec<MonsterDrop>, RepositoryError>;
    
    // Monster Skill queries
    async fn find_monster_skill_by_id(&self, id: &MonsterSkillId) -> Result<Option<MonsterSkill>, RepositoryError>;
    async fn find_monster_skills_by_monster(&self, monster_id: &MonsterId) -> Result<Vec<MonsterSkill>, RepositoryError>;
    
    // Quest Location queries
    async fn find_quest_location_by_id(&self, id: &QuestLocationId) -> Result<Option<QuestLocation>, RepositoryError>;
    async fn find_quest_locations_by_quest(&self, quest_id: &crate::models::quest::value_object::QuestId) -> Result<Vec<QuestLocation>, RepositoryError>;
    
    // Quest Requirement queries
    async fn find_quest_requirement_by_id(&self, id: &QuestRequirementId) -> Result<Option<QuestRequirement>, RepositoryError>;
    async fn find_quest_requirements_by_quest(&self, quest_id: &crate::models::quest::value_object::QuestId) -> Result<Vec<QuestRequirement>, RepositoryError>;
    
    // Quest Reward queries
    async fn find_quest_reward_by_id(&self, id: &QuestRewardId) -> Result<Option<QuestReward>, RepositoryError>;
    async fn find_quest_rewards_by_quest(&self, quest_id: &crate::models::quest::value_object::QuestId) -> Result<Vec<QuestReward>, RepositoryError>;
}
