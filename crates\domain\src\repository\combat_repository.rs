use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        aura::{entity::Aura, value_object::AuraId},
        aura_effect::{entity::AuraEffect, value_object::AuraEffectId},
        enhancement::{entity::Enhancement, value_object::EnhancementId},
        enhancement_pattern::{entity::EnhancementPattern, value_object::EnhancementPatternId},
        skill_aura::{entity::SkillAura, value_object::SkillAuraId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait CombatRepository: Send + Sync {
    // Aura operations
    async fn create_aura(&self, aura: &Aura) -> Result<Aura, RepositoryError>;
    async fn update_aura(&self, aura: &Aura) -> Result<(), RepositoryError>;
    async fn delete_aura(&self, id: &AuraId) -> Result<(), RepositoryError>;
    
    // Aura Effect operations
    async fn create_aura_effect(&self, effect: &AuraEffect) -> Result<AuraEffect, RepositoryError>;
    async fn update_aura_effect(&self, effect: &AuraEffect) -> Result<(), RepositoryError>;
    async fn delete_aura_effect(&self, id: &AuraEffectId) -> Result<(), RepositoryError>;
    
    // Enhancement operations
    async fn create_enhancement(&self, enhancement: &Enhancement) -> Result<Enhancement, RepositoryError>;
    async fn update_enhancement(&self, enhancement: &Enhancement) -> Result<(), RepositoryError>;
    async fn delete_enhancement(&self, id: &EnhancementId) -> Result<(), RepositoryError>;
    
    // Enhancement Pattern operations
    async fn create_enhancement_pattern(&self, pattern: &EnhancementPattern) -> Result<EnhancementPattern, RepositoryError>;
    async fn update_enhancement_pattern(&self, pattern: &EnhancementPattern) -> Result<(), RepositoryError>;
    async fn delete_enhancement_pattern(&self, id: &EnhancementPatternId) -> Result<(), RepositoryError>;
    
    // Skill Aura operations
    async fn create_skill_aura(&self, skill_aura: &SkillAura) -> Result<SkillAura, RepositoryError>;
    async fn update_skill_aura(&self, skill_aura: &SkillAura) -> Result<(), RepositoryError>;
    async fn delete_skill_aura(&self, id: &SkillAuraId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait CombatReadRepository: Send + Sync {
    // Aura queries
    async fn find_aura_by_id(&self, id: &AuraId) -> Result<Option<Aura>, RepositoryError>;
    async fn find_auras_by_name(&self, name: &str) -> Result<Vec<Aura>, RepositoryError>;
    async fn find_all_auras(&self) -> Result<Vec<Aura>, RepositoryError>;
    
    // Aura Effect queries
    async fn find_aura_effect_by_id(&self, id: &AuraEffectId) -> Result<Option<AuraEffect>, RepositoryError>;
    async fn find_aura_effects_by_aura(&self, aura_id: &AuraId) -> Result<Vec<AuraEffect>, RepositoryError>;
    
    // Enhancement queries
    async fn find_enhancement_by_id(&self, id: &EnhancementId) -> Result<Option<Enhancement>, RepositoryError>;
    async fn find_enhancements_by_name(&self, name: &str) -> Result<Vec<Enhancement>, RepositoryError>;
    async fn find_all_enhancements(&self) -> Result<Vec<Enhancement>, RepositoryError>;
    
    // Enhancement Pattern queries
    async fn find_enhancement_pattern_by_id(&self, id: &EnhancementPatternId) -> Result<Option<EnhancementPattern>, RepositoryError>;
    async fn find_enhancement_patterns_by_enhancement(&self, enhancement_id: &EnhancementId) -> Result<Vec<EnhancementPattern>, RepositoryError>;
    
    // Skill Aura queries
    async fn find_skill_aura_by_id(&self, id: &SkillAuraId) -> Result<Option<SkillAura>, RepositoryError>;
    async fn find_skill_auras_by_skill(&self, skill_id: &crate::models::skill::value_object::SkillId) -> Result<Vec<SkillAura>, RepositoryError>;
    async fn find_skill_auras_by_aura(&self, aura_id: &AuraId) -> Result<Vec<SkillAura>, RepositoryError>;
}
