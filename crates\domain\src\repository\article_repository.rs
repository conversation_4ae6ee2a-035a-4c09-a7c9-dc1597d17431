use async_trait::async_trait;

use crate::{
    error::RepositoryError,
    models::{
        article::{entity::Article, value_object::ArticlePostId},
        cms_article::{entity::CMSArticle, value_object::CMSArticleId},
    },
};

#[mockall::automock]
#[async_trait]
pub trait ArticleRepository: Send + Sync {
    // Article operations
    async fn create(&self, article: &Article) -> Result<Article, RepositoryError>;
    async fn update(&self, article: &Article) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &ArticlePostId) -> Result<(), RepositoryError>;

    // CMS Article operations
    async fn create_cms_article(&self, article: &CMSArticle) -> Result<CMSArticle, RepositoryError>;
    async fn update_cms_article(&self, article: &CMSArticle) -> Result<(), RepositoryError>;
    async fn delete_cms_article(&self, id: &CMSArticleId) -> Result<(), RepositoryError>;
}

#[mockall::automock]
#[async_trait]
pub trait ArticleReadRepository: Send + Sync {
    // Article queries
    async fn find_by_id(&self, id: &ArticlePostId) -> Result<Option<Article>, RepositoryError>;
    async fn find_by_title(&self, title: &str) -> Result<Vec<Article>, RepositoryError>;
    async fn find_all(&self) -> Result<Vec<Article>, RepositoryError>;
    async fn find_paginate(&self, page: u32, limit: u32) -> Result<Vec<Article>, RepositoryError>;
    async fn find_published(&self) -> Result<Vec<Article>, RepositoryError>;

    // CMS Article queries
    async fn find_cms_article_by_id(&self, id: &CMSArticleId) -> Result<Option<CMSArticle>, RepositoryError>;
    async fn find_cms_articles_by_title(&self, title: &str) -> Result<Vec<CMSArticle>, RepositoryError>;
    async fn find_all_cms_articles(&self) -> Result<Vec<CMSArticle>, RepositoryError>;
    async fn find_cms_articles_paginate(&self, page: u32, limit: u32) -> Result<Vec<CMSArticle>, RepositoryError>;
    async fn find_published_cms_articles(&self) -> Result<Vec<CMSArticle>, RepositoryError>;
}
